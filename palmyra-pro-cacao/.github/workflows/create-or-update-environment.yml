name: Create or Update an environment
run-name: >
  Create or Update ${{ inputs.shortName != '' && inputs.shortName || format('pr-{0}-{1}', github.event.pull_request.number, github.event.pull_request.user.login) }}

on:
  pull_request:
    types: [opened, reopened, edited]
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: "Environment"
        required: true
        default: "development"
      shortName:
        type: string
        description: "staging, prod or any other short name used as prefix for resources, labels, etc."
        required: true
        default: "PR"
      seedDev:
        type: boolean
        description: "Seed database with dev data"
        required: true
        default: true

jobs:
  setup:
    name: Setup and Configuration
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    outputs:
      short_name: ${{ steps.set-env.outputs.short_name }}
      tag_name: ${{ steps.set-env.outputs.tag_name }}
      channel_id: ${{ steps.set-env.outputs.channel_id }}
      min_instances: ${{ steps.set-env.outputs.min_instances }}
      gcp_project_id: ${{ steps.set-env.outputs.gcp_project_id }}
      gcp_project_number: ${{ steps.set-env.outputs.gcp_project_number }}
      gcp_db_password_secret_name: ${{ steps.set-env.outputs.gcp_db_password_secret_name }}
      db_name: ${{ steps.set-env.outputs.db_name }}
      db_host: ${{ steps.set-env.outputs.db_host }}
      db_password: ${{ steps.set-env.outputs.db_password }}
      db_user: ${{ steps.set-env.outputs.db_user }}
      db_port: ${{ steps.set-env.outputs.db_port }}
      fe_url: ${{ steps.set-env.outputs.fe_url }}
      api_url: ${{ steps.set-env.outputs.api_url }}
      db_instance_connection_name: ${{ steps.set-env.outputs.db_instance_connection_name }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Set dynamic ENV
        id: set-env
        run: |
          # If inputs.shortName is not present, it means its a PR.
          INPUTS_SHORT_NAME="${{ inputs.shortName || 'PR' }}"

          if [[ "${INPUTS_SHORT_NAME}" == "prod" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="latest"
            FE_URL="https://nn-cacao.palmyra.pro"

            echo "FE_URL=${TMP_PREVIEW_URL}" >> $GITHUB_ENV

            # To avoid mistakes, let's don't allow prod deployment into development.
            if [[ "${{ inputs.environment || 'development' }}" != "production" ]]; then
              exit 1
            fi
          elif [[ "${INPUTS_SHORT_NAME}" == "staging" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="staging-latest"
            FE_URL="https://nn-cacao-dev.web.app"
          elif [[ "${INPUTS_SHORT_NAME}" == "PR" ]]; then
            RAW_NAME="pr-${{ github.event.pull_request.number }}-${{ github.event.pull_request.user.login }}"
            SHORT_NAME=$(echo "${RAW_NAME}" | tr '[:upper:]' '[:lower:]' | tr -c 'a-z0-9-' '-' | cut -c1-40)
            CHANNEL_ID="${SHORT_NAME}"
            TAG_NAME="${SHORT_NAME}"
            FE_URL=""
          else
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="${INPUTS_SHORT_NAME}"
            TAG_NAME="${INPUTS_SHORT_NAME}"
            FE_URL=""
          fi

          echo "SHORT_NAME=$SHORT_NAME" >> $GITHUB_ENV
          echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV
          echo "CHANNEL_ID=$CHANNEL_ID" >> $GITHUB_ENV

          if [[ "$SHORT_NAME" == "staging" ]]; then
            MIN_INSTANCES=1
          else
            MIN_INSTANCES=0
          fi
          echo "MIN_INSTANCES=$MIN_INSTANCES" >> $GITHUB_ENV

          echo "GCP_PROJECT_ID=${{ vars.GCP_PROJECT_ID }}" >> $GITHUB_ENV
          GCP_PROJECT_NUMBER=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')
          echo "GCP_PROJECT_NUMBER=${GCP_PROJECT_NUMBER}" >> $GITHUB_ENV
          echo "GCP_DB_PASSWORD_SECRET_NAME=DB_PASSWORD_$(echo "${{ vars.DB_INSTANCE }}" | tr '[:lower:]' '[:upper:]' | tr '-' '_')" >> $GITHUB_ENV

          echo "DB_NAME=palmyrapro-db-$SHORT_NAME" >> $GITHUB_ENV
          # echo "DB_HOST=$(gcloud sql instances describe ${{ vars.DB_INSTANCE }} --format=json | jq -r '.ipAddresses[] | select(.type == "PRIMARY") | .ipAddress')" >> $GITHUB_ENV
          DB_INSTANCE_CONNECTION_NAME="${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"
          echo "DB_HOST=/cloudsql/${DB_INSTANCE_CONNECTION_NAME}" >> $GITHUB_ENV
          echo "DB_PASSWORD=${{ secrets.DB_PASSWORD }}" >>  $GITHUB_ENV
          echo "DB_USER=${{ vars.DB_USER }}" >> $GITHUB_ENV
          echo "DB_PORT=${{ vars.DB_PORT }}" >> $GITHUB_ENV

          echo "FE_URL=${FE_URL}" >> $GITHUB_ENV
          echo "API_URL=https://$SHORT_NAME-cacao-api-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app" >> $GITHUB_ENV

          # Set outputs for other jobs
          echo "short_name=$SHORT_NAME" >> $GITHUB_OUTPUT
          echo "tag_name=$TAG_NAME" >> $GITHUB_OUTPUT
          echo "channel_id=$CHANNEL_ID" >> $GITHUB_OUTPUT
          echo "min_instances=$MIN_INSTANCES" >> $GITHUB_OUTPUT
          echo "gcp_project_id=${{ vars.GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
          echo "gcp_project_number=${GCP_PROJECT_NUMBER}" >> $GITHUB_OUTPUT
          echo "gcp_db_password_secret_name=DB_PASSWORD_$(echo "${{ vars.DB_INSTANCE }}" | tr '[:lower:]' '[:upper:]' | tr '-' '_')" >> $GITHUB_OUTPUT
          echo "db_name=palmyrapro-db-$SHORT_NAME" >> $GITHUB_OUTPUT
          echo "db_host=/cloudsql/${DB_INSTANCE_CONNECTION_NAME}" >> $GITHUB_OUTPUT
          echo "db_password=${{ secrets.DB_PASSWORD }}" >> $GITHUB_OUTPUT
          echo "db_user=${{ vars.DB_USER }}" >> $GITHUB_OUTPUT
          echo "db_port=${{ vars.DB_PORT }}" >> $GITHUB_OUTPUT
          echo "fe_url=${FE_URL}" >> $GITHUB_OUTPUT
          echo "api_url=https://$SHORT_NAME-cacao-api-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app" >> $GITHUB_OUTPUT
          echo "db_instance_connection_name=${DB_INSTANCE_CONNECTION_NAME}" >> $GITHUB_OUTPUT

  database:
    name: Database Setup
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    needs: setup
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Install Cloud SQL Auth Proxy
        run: |
          curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.15.2/cloud-sql-proxy.linux.amd64
          chmod +x cloud-sql-proxy
          sudo mv cloud-sql-proxy /usr/local/bin/

      - name: Start Cloud SQL Proxy
        run: |
          cloud-sql-proxy ${{ needs.setup.outputs.db_instance_connection_name }} --credentials-file=$GOOGLE_APPLICATION_CREDENTIALS --port 5432 &

          # Wait and check if port is open
          for i in {1..10}; do
            echo "Checking if proxy is up (attempt $i)..."
            nc -z 127.0.0.1 5432 && break
            sleep 2
          done

          # Final check
          if ! nc -z 127.0.0.1 5432; then
            echo "❌ Cloud SQL Proxy failed to start."
            exit 1
          fi

          echo "✅ Cloud SQL Proxy is up and running."

      - name: Check if database exists
        id: check-db
        env:
          PGPASSWORD: ${{ needs.setup.outputs.db_password }}
        run: |
          DB_EXISTS=$(psql -h 127.0.0.1 -U ${{ needs.setup.outputs.db_user }} -d postgres \
            -tAc "SELECT 1 FROM pg_database WHERE datname = '${{ needs.setup.outputs.db_name }}'" | grep -q 1 && echo true || echo false)

          echo "exists=$DB_EXISTS" >> $GITHUB_OUTPUT

      - name: Create Cloud SQL schema
        if: steps.check-db.outputs.exists == 'false'
        run: |
          gcloud sql databases create ${{ needs.setup.outputs.db_name }} --instance=${{ vars.DB_INSTANCE }}

      - name: Run DB migrations / init scripts
        if: steps.check-db.outputs.exists == 'false'
        env:
          PGPASSWORD: ${{ needs.setup.outputs.db_password }}
        run: |
          psql "host=127.0.0.1 port=5432 user=${{ needs.setup.outputs.db_user }} dbname=${{ needs.setup.outputs.db_name }}" -f "packages/db/schema/01-ddl.sql"
          psql "host=127.0.0.1 port=5432 user=${{ needs.setup.outputs.db_user }} dbname=${{ needs.setup.outputs.db_name }}" -f "packages/db/schema/02-seeding.sql"

  build-frontend:
    name: Build Frontend
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    needs: setup
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      # TODO: From here, should be in a external Deploy Frontend workflow and reuse it.
      - name: FE. Build
        env:
          NEXT_PUBLIC_BACKEND_URL: "${{ needs.setup.outputs.api_url }}"
          CI: "true"
          SKIP_TYPE_CHECK: "true"
          NEXT_PUBLIC_FIREBASE_API_KEY: "${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}"
          NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: "${{ vars.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}"
          NEXT_PUBLIC_FIREBASE_PROJECT_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}"
          NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: "${{ vars.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}"
          NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}"
          NEXT_PUBLIC_FIREBASE_APP_ID: "${{ secrets.NEXT_PUBLIC_FIREBASE_APP_ID }}"
        run: |
          npm ci
          npm run generate --workspace=packages/api-specs
          npm run build --workspace=apps/frontend
          du -sh apps/frontend/dist/.

  deploy-frontend:
    name: Deploy Frontend
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    needs: [setup, build-frontend]
    outputs:
      preview_url: ${{ steps.deploy.outputs.PREVIEW_URL }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Rebuild Frontend (for deployment)
        env:
          NEXT_PUBLIC_BACKEND_URL: "${{ needs.setup.outputs.api_url }}"
          CI: "true"
          SKIP_TYPE_CHECK: "true"
          NEXT_PUBLIC_FIREBASE_API_KEY: "${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}"
          NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: "${{ vars.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}"
          NEXT_PUBLIC_FIREBASE_PROJECT_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}"
          NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: "${{ vars.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}"
          NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}"
          NEXT_PUBLIC_FIREBASE_APP_ID: "${{ secrets.NEXT_PUBLIC_FIREBASE_APP_ID }}"
        run: |
          npm ci
          npm run generate --workspace=packages/api-specs
          npm run build --workspace=apps/frontend
          du -sh apps/frontend/dist/.

      - name: Deploy to Firebase Hosting
        id: deploy
        working-directory: apps/frontend
        env:
          NODE_OPTIONS: "--trace-deprecation"
        run: |
          npm install -g firebase-tools

          # Deploy
          TMP_OUTPUT=$(mktemp)

          EXIT_CODE=0
          if [ "${{ needs.setup.outputs.channel_id }}" == "live" ]; then
            firebase deploy \
            --only hosting \
            --project="${{ vars.GCP_PROJECT_ID }}" \
            --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL="https://${{ vars.GCP_PROJECT_ID }}.web.app/"

            echo "✅ Preview URL: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"

          else
            firebase hosting:channel:deploy "${{ needs.setup.outputs.channel_id }}" \
              --project="${{ vars.GCP_PROJECT_ID }}" \
              --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL=$(jq -r '.result[].url' ${TMP_OUTPUT})
            echo "✅ Preview URL: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"

            echo "FE_URL=${TMP_PREVIEW_URL}" >> $GITHUB_ENV
          fi

  build-api:
    name: Build API
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    needs: setup
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Configure Docker
        run: gcloud auth configure-docker "${{ vars.GCP_REGION }}-docker.pkg.dev"

      - name: API. Build and Push Docker Image
        run: |
          docker build -t ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:${{ needs.setup.outputs.tag_name }} -f ./apps/api/Dockerfile .
          docker push ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:${{ needs.setup.outputs.tag_name }}

  deploy-api:
    name: Deploy API
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    needs: [setup, database, build-api]
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: API. Deploy to Cloud Run
        run: |
          # Debug: Print environment variables
          # echo "=== Environment Variables ==="
          # echo "SHORT_NAME: ${{ needs.setup.outputs.short_name }}"
          # echo "DB_NAME: ${{ needs.setup.outputs.db_name }}"
          # echo "METABASE_SITE_URL: ${{ vars.METABASE_SITE_URL }}"
          # echo "ALLOWED_ORIGIN: ${{ needs.setup.outputs.fe_url }}"

          # Deploy with volume mount for Firebase secret
          gcloud run deploy ${{ needs.setup.outputs.short_name }}-cacao-api \
            --image ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:${{ needs.setup.outputs.tag_name }} \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --execution-environment gen2 \
            --min-instances ${{ needs.setup.outputs.min_instances }} \
            --max-instances 2 \
            --concurrency 1000 \
            --allow-unauthenticated \
            --port 3000 \
            --add-cloudsql-instances ${{ needs.setup.outputs.db_instance_connection_name }} \
            --set-env-vars "DB_NAME=${{ needs.setup.outputs.db_name }},DB_HOST=${{ needs.setup.outputs.db_host }},DB_PORT=${{ needs.setup.outputs.db_port }},DB_USER=${{ needs.setup.outputs.db_user }},DB_SSL_MODE=${{ vars.DB_SSL_MODE }}" \
            --set-env-vars "ALLOWED_ORIGIN=${{ needs.setup.outputs.fe_url }},METABASE_SITE_URL=${{ vars.METABASE_SITE_URL }},ENV_SHORT_NAME=${{ needs.setup.outputs.short_name }}" \
            --set-env-vars "FIREBASE_ADMIN_KEY_PATH=/secrets/FIREBASE_SERVICE_ACCOUNT_KEY,FIREBASE_AUTH_SECRET=${{ vars.FIREBASE_AUTH_SECRET }},FE_URL=${{ needs.setup.outputs.fe_url }}" \
            --set-secrets "/secrets/FIREBASE_SERVICE_ACCOUNT_KEY=FIREBASE_SERVICE_ACCOUNT_KEY:latest,DB_PASSWORD=${{ needs.setup.outputs.gcp_db_password_secret_name }}:latest,METABASE_SECRET_KEY=METABASE_SECRET_KEY:latest" \
            --labels "env_short_name=${{ needs.setup.outputs.short_name }}"

          # TODO: Better if Firebase is routing this service into a static context path
          gcloud run services add-iam-policy-binding ${{ needs.setup.outputs.short_name }}-cacao-api \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --member="allUsers" \
            --role="roles/run.invoker"

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [setup, deploy-frontend, deploy-api]
    if: always()
    steps:
      - name: Comment on PR with preview URL
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ✅ Preview deployed to: ${{ needs.deploy-frontend.outputs.preview_url }}

      - name: API. Comment on PR with endpoint URL
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ✅ Cacao API deployed to: "${{ needs.setup.outputs.api_url }}"
