name: Delete an environment
run-name: >
  Delete ${{ inputs.shortName != '' && inputs.shortName || format('pr-{0}-{1}', github.event.pull_request.number, github.event.pull_request.user.login) }}

on:
  pull_request:
    types: [closed]
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: "Environment"
        required: true
        default: "development"
      shortName:
        type: string
        description: "Short name used, for example, as prefix for resources, labels, etc."
        required: true
        default: "PR"

jobs:
  setup:
    name: Setup and Configuration
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    outputs:
      short_name: ${{ steps.set-env.outputs.short_name }}
      tag_name: ${{ steps.set-env.outputs.tag_name }}
      channel_id: ${{ steps.set-env.outputs.channel_id }}
      gcp_project_id: ${{ steps.set-env.outputs.gcp_project_id }}
      gcp_project_number: ${{ steps.set-env.outputs.gcp_project_number }}
      gcp_db_password_secret_name: ${{ steps.set-env.outputs.gcp_db_password_secret_name }}
      db_name: ${{ steps.set-env.outputs.db_name }}
      db_host: ${{ steps.set-env.outputs.db_host }}
      db_password: ${{ steps.set-env.outputs.db_password }}
      db_user: ${{ steps.set-env.outputs.db_user }}
      db_port: ${{ steps.set-env.outputs.db_port }}
      db_instance_connection_name: ${{ steps.set-env.outputs.db_instance_connection_name }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Set dynamic ENV
        id: set-env
        run: |
          # If inputs.shortName is not present, it means its a PR.
          INPUTS_SHORT_NAME="${{ inputs.shortName || 'PR' }}"

          if [[ "${INPUTS_SHORT_NAME}" == "prod" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="latest"

            # For security reasons, let's don't allow demore production.
            exit 1
          elif [[ "${INPUTS_SHORT_NAME}" == "staging" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="staging-latest"
          elif [[ "${INPUTS_SHORT_NAME}" == "PR" ]]; then
            RAW_NAME="pr-${{ github.event.pull_request.number }}-${{ github.event.pull_request.user.login }}"
            SHORT_NAME=$(echo "${RAW_NAME}" | tr '[:upper:]' '[:lower:]' | tr -c 'a-z0-9-' '-' | cut -c1-40)
            CHANNEL_ID="${SHORT_NAME}"
            TAG_NAME="${SHORT_NAME}"
          else
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="${INPUTS_SHORT_NAME}"
            TAG_NAME="${INPUTS_SHORT_NAME}"
          fi

          echo "SHORT_NAME=$SHORT_NAME" >> $GITHUB_ENV
          echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV
          echo "CHANNEL_ID=$CHANNEL_ID" >> $GITHUB_ENV

          echo "GCP_PROJECT_ID=${{ vars.GCP_PROJECT_ID }}" >> $GITHUB_ENV
          echo "GCP_PROJECT_NUMBER=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')" >> $GITHUB_ENV
          echo "GCP_DB_PASSWORD_SECRET_NAME=DB_PASSWORD_$(echo "${{ vars.DB_INSTANCE }}" | tr '[:lower:]' '[:upper:]' | tr '-' '_')" >> $GITHUB_ENV

          echo "DB_NAME=palmyrapro-db-$SHORT_NAME" >> $GITHUB_ENV
          # echo "DB_HOST=$(gcloud sql instances describe ${{ vars.DB_INSTANCE }} --format=json | jq -r '.ipAddresses[] | select(.type == "PRIMARY") | .ipAddress')" >> $GITHUB_ENV
          DB_INSTANCE_CONNECTION_NAME="${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"
          echo "DB_HOST=/cloudsql/${DB_INSTANCE_CONNECTION_NAME}" >> $GITHUB_ENV
          echo "DB_PASSWORD=${{ secrets.DB_PASSWORD }}" >>  $GITHUB_ENV
          echo "DB_USER=${{ vars.DB_USER}}" >> $GITHUB_ENV
          echo "DB_PORT=${{ vars.DB_PORT}}" >> $GITHUB_ENV

          # Set outputs for other jobs
          echo "short_name=$SHORT_NAME" >> $GITHUB_OUTPUT
          echo "tag_name=$TAG_NAME" >> $GITHUB_OUTPUT
          echo "channel_id=$CHANNEL_ID" >> $GITHUB_OUTPUT
          echo "gcp_project_id=${{ vars.GCP_PROJECT_ID }}" >> $GITHUB_OUTPUT
          echo "gcp_project_number=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')" >> $GITHUB_OUTPUT
          echo "gcp_db_password_secret_name=DB_PASSWORD_$(echo "${{ vars.DB_INSTANCE }}" | tr '[:lower:]' '[:upper:]' | tr '-' '_')" >> $GITHUB_OUTPUT
          echo "db_name=palmyrapro-db-$SHORT_NAME" >> $GITHUB_OUTPUT
          echo "db_host=/cloudsql/${DB_INSTANCE_CONNECTION_NAME}" >> $GITHUB_OUTPUT
          echo "db_password=${{ secrets.DB_PASSWORD }}" >> $GITHUB_OUTPUT
          echo "db_user=${{ vars.DB_USER}}" >> $GITHUB_OUTPUT
          echo "db_port=${{ vars.DB_PORT}}" >> $GITHUB_OUTPUT
          echo "db_instance_connection_name=${DB_INSTANCE_CONNECTION_NAME}" >> $GITHUB_OUTPUT

  delete-frontend:
    name: Delete Frontend
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    needs: setup
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: FE. Delete Frontend
        working-directory: apps/frontend
        run: |
          npm install -g firebase-tools
          firebase hosting:channel:delete "${{ needs.setup.outputs.channel_id }}" --project="${{ vars.GCP_PROJECT_ID }}"

  delete-api:
    name: Delete API
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    needs: setup
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: API. Delete from Cloud Run
        run: |
          gcloud run services delete ${{ needs.setup.outputs.short_name }}-cacao-api --quiet \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }}

  delete-database:
    name: Delete Database
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    needs: [setup, delete-frontend, delete-api]
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Install Cloud SQL Auth Proxy
        run: |
          curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.15.2/cloud-sql-proxy.linux.amd64
          chmod +x cloud-sql-proxy
          sudo mv cloud-sql-proxy /usr/local/bin/

      - name: Start Cloud SQL Proxy
        run: |
          cloud-sql-proxy ${{ needs.setup.outputs.db_instance_connection_name }} --credentials-file=$GOOGLE_APPLICATION_CREDENTIALS --port 5432 &

          # Wait and check if port is open
          for i in {1..10}; do
            echo "Checking if proxy is up (attempt $i)..."
            nc -z 127.0.0.1 5432 && break
            sleep 2
          done

          # Final check
          if ! nc -z 127.0.0.1 5432; then
            echo "❌ Cloud SQL Proxy failed to start."
            exit 1
          fi

          echo "✅ Cloud SQL Proxy is up and running."

      - name: Check if database exists
        id: check-db
        env:
          PGPASSWORD: ${{ needs.setup.outputs.db_password }}
        run: |
          DB_EXISTS=$(psql -h 127.0.0.1 -U ${{ needs.setup.outputs.db_user }} -d postgres \
            -tAc "SELECT 1 FROM pg_database WHERE datname = '${{ needs.setup.outputs.db_name }}'" | grep -q 1 && echo true || echo false)

          echo "exists=$DB_EXISTS" >> $GITHUB_OUTPUT

      - name: Delete Cloud SQL schema
        if: steps.check-db.outputs.exists == 'true'
        run: |
          gcloud sql databases delete ${{ needs.setup.outputs.db_name }} --instance=${{ vars.DB_INSTANCE }} --quiet
